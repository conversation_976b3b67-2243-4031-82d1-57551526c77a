import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/shared/lib/css'

const gaugeVariants = cva('relative inline-flex items-center justify-center', {
    variants: {
        size: {
            sm: 'size-24',
            default: 'size-36',
            lg: 'size-48',
        },
    },
    defaultVariants: {
        size: 'default',
    },
})

interface GaugeProps
    extends React.HTMLAttributes<HTMLDivElement>,
        VariantProps<typeof gaugeVariants> {
    value: number
    activeColor?: string
    inactiveColor?: string
    showValue?: boolean
    valuePosition?: 'center' | 'bottom' | 'top'
}

function Gauge({
    className,
    value,
    activeColor = '#FF7D7F',
    inactiveColor = '#E5E7EB',
    size,
    showValue = true,
    valuePosition = 'center',
    ...props
}: GaugeProps) {
    // Clamp value between 0 and 100
    const clampedValue = Math.max(0, Math.min(100, value))

    // Calculate rotation angle for the needle (180 degrees range for semicircle)
    const needleRotation = (clampedValue / 100) * 180 - 90 // -90 to start from left

    const sizeMap = {
        sm: { width: 96, height: 48, radius: 35, strokeWidth: 6, needleLength: 25 },
        default: { width: 144, height: 72, radius: 45, strokeWidth: 8, needleLength: 35 },
        lg: { width: 192, height: 96, radius: 60, strokeWidth: 10, needleLength: 45 },
    }

    const dimensions = sizeMap[size || 'default']

    // Calculate the arc path for semicircle
    const centerX = dimensions.width / 2
    const centerY = dimensions.height - dimensions.strokeWidth
    const startX = dimensions.strokeWidth
    const startY = centerY
    const endX = dimensions.width - dimensions.strokeWidth
    const endY = centerY

    return (
        <div
            className={cn(gaugeVariants({ size, className }))}
            {...props}
        >
            <svg
                width={dimensions.width}
                height={dimensions.height}
                viewBox={`0 0 ${dimensions.width} ${dimensions.height}`}
                className="absolute"
            >
                {/* Background arc */}
                <path
                    d={`M ${dimensions.strokeWidth} ${dimensions.height - dimensions.strokeWidth} A ${dimensions.radius} ${dimensions.radius} 0 0 1 ${dimensions.width - dimensions.strokeWidth} ${dimensions.height - dimensions.strokeWidth}`}
                    fill="none"
                    stroke={inactiveColor}
                    strokeWidth={dimensions.strokeWidth}
                    strokeLinecap="round"
                />

                {/* Active arc */}
                <path
                    d={`M ${dimensions.strokeWidth} ${dimensions.height - dimensions.strokeWidth} A ${dimensions.radius} ${dimensions.radius} 0 0 1 ${dimensions.width - dimensions.strokeWidth} ${dimensions.height - dimensions.strokeWidth}`}
                    fill="none"
                    stroke={activeColor}
                    strokeWidth={dimensions.strokeWidth}
                    strokeLinecap="round"
                    strokeDasharray={`${(clampedValue / 100) * Math.PI * dimensions.radius} ${Math.PI * dimensions.radius}`}
                    className="transition-all duration-500 ease-out"
                />

                {/* Needle */}
                <g
                    transform={`translate(${dimensions.width / 2}, ${dimensions.height - dimensions.strokeWidth})`}
                >
                    <line
                        x1="0"
                        y1="0"
                        x2={dimensions.needleLength}
                        y2="0"
                        stroke={activeColor}
                        strokeWidth="2"
                        strokeLinecap="round"
                        transform={`rotate(${needleRotation})`}
                        className="transition-transform duration-500 ease-out"
                    />
                    {/* Needle center dot */}
                    <circle
                        cx="0"
                        cy="0"
                        r="3"
                        fill={activeColor}
                    />
                </g>
            </svg>

            {/* Value display */}
            {showValue && (
                <div
                    className={cn('absolute text-sm font-medium text-foreground', {
                        'top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2':
                            valuePosition === 'center',
                        'bottom-2 left-1/2 -translate-x-1/2': valuePosition === 'bottom',
                        'top-2 left-1/2 -translate-x-1/2': valuePosition === 'top',
                    })}
                >
                    {Math.round(clampedValue)}%
                </div>
            )}
        </div>
    )
}

export { Gauge, gaugeVariants }
export type { GaugeProps }
