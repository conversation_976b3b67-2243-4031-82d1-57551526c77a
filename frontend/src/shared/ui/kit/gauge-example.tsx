import React, { useState } from 'react'
import { Gauge } from './gauge'
import { Button } from './button'
import { Slider } from './slider'

/**
 * Example usage of the Gauge component
 * This file demonstrates how to integrate the gauge component in a real application
 */
export function GaugeExample() {
    const [value, setValue] = useState(75)
    const [activeColor, setActiveColor] = useState('#FF7D7F')
    const [size, setSize] = useState<'sm' | 'default' | 'lg'>('default')

    const presetColors = [
        { name: 'Red (Default)', color: '#FF7D7F' },
        { name: 'Green', color: '#10B981' },
        { name: 'Blue', color: '#3B82F6' },
        { name: 'Orange', color: '#F59E0B' },
        { name: 'Purple', color: '#8B5CF6' },
    ]

    const presetValues = [
        { name: 'Low', value: 25 },
        { name: 'Medium', value: 50 },
        { name: 'High', value: 75 },
        { name: 'Full', value: 100 },
    ]

    return (
        <div className="p-8 space-y-8 max-w-4xl mx-auto">
            <div className="text-center">
                <h1 className="text-3xl font-bold mb-2">Gauge Component Demo</h1>
                <p className="text-muted-foreground">
                    Interactive demonstration of the gauge component with various configurations
                </p>
            </div>

            {/* Main Gauge Display */}
            <div className="flex justify-center">
                <Gauge
                    value={value}
                    activeColor={activeColor}
                    size={size}
                    showValue={true}
                    valuePosition="center"
                />
            </div>

            {/* Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Value Control */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Value Control</h3>
                    <div className="space-y-2">
                        <label className="text-sm font-medium">
                            Current Value: {value}%
                        </label>
                        <Slider
                            value={[value]}
                            onValueChange={(values) => setValue(values[0])}
                            max={100}
                            min={0}
                            step={1}
                            className="w-full"
                        />
                    </div>
                    <div className="flex gap-2 flex-wrap">
                        {presetValues.map((preset) => (
                            <Button
                                key={preset.name}
                                variant="outline"
                                size="sm"
                                onClick={() => setValue(preset.value)}
                            >
                                {preset.name} ({preset.value}%)
                            </Button>
                        ))}
                    </div>
                </div>

                {/* Color Control */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Color Control</h3>
                    <div className="grid grid-cols-1 gap-2">
                        {presetColors.map((preset) => (
                            <Button
                                key={preset.name}
                                variant={activeColor === preset.color ? 'default' : 'outline'}
                                size="sm"
                                onClick={() => setActiveColor(preset.color)}
                                className="justify-start"
                            >
                                <div
                                    className="w-4 h-4 rounded-full mr-2"
                                    style={{ backgroundColor: preset.color }}
                                />
                                {preset.name}
                            </Button>
                        ))}
                    </div>
                </div>
            </div>

            {/* Size Control */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Size Control</h3>
                <div className="flex gap-2">
                    {(['sm', 'default', 'lg'] as const).map((sizeOption) => (
                        <Button
                            key={sizeOption}
                            variant={size === sizeOption ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setSize(sizeOption)}
                        >
                            {sizeOption === 'default' ? 'Default' : sizeOption.toUpperCase()}
                        </Button>
                    ))}
                </div>
            </div>

            {/* Multiple Gauges Example */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Multiple Gauges Example</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center space-y-2">
                        <Gauge value={30} activeColor="#EF4444" size="sm" />
                        <div>
                            <p className="font-medium">CPU Usage</p>
                            <p className="text-sm text-muted-foreground">30%</p>
                        </div>
                    </div>
                    <div className="text-center space-y-2">
                        <Gauge value={65} activeColor="#F59E0B" size="default" />
                        <div>
                            <p className="font-medium">Memory Usage</p>
                            <p className="text-sm text-muted-foreground">65%</p>
                        </div>
                    </div>
                    <div className="text-center space-y-2">
                        <Gauge value={85} activeColor="#10B981" size="lg" />
                        <div>
                            <p className="font-medium">Disk Usage</p>
                            <p className="text-sm text-muted-foreground">85%</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Code Example */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold">Code Example</h3>
                <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm overflow-x-auto">
                        <code>{`<Gauge
  value={${value}}
  activeColor="${activeColor}"
  size="${size}"
  showValue={true}
  valuePosition="center"
/>`}</code>
                    </pre>
                </div>
            </div>
        </div>
    )
}
