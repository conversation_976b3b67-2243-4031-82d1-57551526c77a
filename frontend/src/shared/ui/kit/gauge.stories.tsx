import type { Meta, StoryObj } from '@storybook/react-vite'
import { Gauge } from './gauge'

const meta = {
    component: Gauge,
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        value: {
            control: { type: 'range', min: 0, max: 100, step: 1 },
        },
        activeColor: {
            control: { type: 'color' },
        },
        inactiveColor: {
            control: { type: 'color' },
        },
        size: {
            control: { type: 'select' },
            options: ['sm', 'default', 'lg'],
        },
        showValue: {
            control: { type: 'boolean' },
        },
        valuePosition: {
            control: { type: 'select' },
            options: ['center', 'bottom', 'top'],
        },
    },
} satisfies Meta<typeof Gauge>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    name: 'Default - 75% Progress',
    args: {
        value: 75,
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
}

export const Empty: Story = {
    name: 'Empty - 0% Progress',
    args: {
        value: 0,
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
}

export const Full: Story = {
    name: 'Full - 100% Progress',
    args: {
        value: 100,
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
}

export const CustomColors: Story = {
    name: 'Custom Colors - Green Theme',
    args: {
        value: 60,
        activeColor: '#10B981',
        inactiveColor: '#D1D5DB',
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
}

export const SmallSize: Story = {
    name: 'Small Size',
    args: {
        value: 45,
        size: 'sm',
        showValue: true,
        valuePosition: 'center',
    },
}

export const LargeSize: Story = {
    name: 'Large Size',
    args: {
        value: 85,
        size: 'lg',
        showValue: true,
        valuePosition: 'center',
    },
}

export const NoValue: Story = {
    name: 'Without Value Display',
    args: {
        value: 65,
        size: 'default',
        showValue: false,
    },
}

export const ValueBottom: Story = {
    name: 'Value at Bottom',
    args: {
        value: 30,
        size: 'default',
        showValue: true,
        valuePosition: 'bottom',
    },
}

export const ValueTop: Story = {
    name: 'Value at Top',
    args: {
        value: 90,
        size: 'default',
        showValue: true,
        valuePosition: 'top',
    },
}

export const RedAccent: Story = {
    name: 'Red Accent - Figma Design',
    args: {
        value: 75,
        activeColor: '#FF7D7F',
        inactiveColor: '#E5E7EB',
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
}

export const Interactive: Story = {
    name: 'Interactive Demo',
    args: {
        value: 50,
        size: 'default',
        showValue: true,
        valuePosition: 'center',
    },
    render: (args) => {
        return (
            <div className="space-y-4">
                <Gauge {...args} />
                <div className="text-center text-sm text-muted-foreground">
                    Use the controls panel to adjust the gauge properties
                </div>
            </div>
        )
    },
}

export const MultipleGauges: Story = {
    name: 'Multiple Gauges Comparison',
    render: () => {
        return (
            <div className="grid grid-cols-3 gap-8 items-center">
                <div className="text-center space-y-2">
                    <Gauge value={25} activeColor="#EF4444" size="sm" />
                    <p className="text-xs text-muted-foreground">Low</p>
                </div>
                <div className="text-center space-y-2">
                    <Gauge value={65} activeColor="#F59E0B" size="default" />
                    <p className="text-xs text-muted-foreground">Medium</p>
                </div>
                <div className="text-center space-y-2">
                    <Gauge value={90} activeColor="#10B981" size="lg" />
                    <p className="text-xs text-muted-foreground">High</p>
                </div>
            </div>
        )
    },
}
