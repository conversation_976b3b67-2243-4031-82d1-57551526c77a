import * as React from 'react'

import { cn } from '@/shared/lib/css'
import type { LucideIcon } from 'lucide-react'

function Input({
    className,
    StartIcon,
    EndIcon,
    type,
    ...props
}: { StartIcon?: LucideIcon; EndIcon?: LucideIcon } & React.ComponentProps<'input'>) {
    return (
        <div className="relative inline-flex items-center">
            {StartIcon && (
                <div className="absolute left-3 flex items-center justify-center h-full pointer-events-none">
                    <StartIcon
                        className="text-muted-foreground"
                        size={18}
                    />
                </div>
            )}
            <input
                type={type}
                data-slot="input"
                className={cn(
                    'file:text-foreground placeholder:text-toggle-gray selection:bg-primary selection:text-primary-foreground dark:bg-input/30 flex h-12 w-full min-w-0 rounded-md border border-transparent bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-base',
                    'hover:border-toggle-gray focus-visible:border-toggle-gray',
                    'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
                    className,
                )}
                style={{
                    paddingLeft: StartIcon ? '40px' : '16px',
                    paddingRight: EndIcon ? '40px' : '16px',
                }}
                {...props}
            />
            {EndIcon && (
                <div className="absolute right-3 flex items-center justify-center h-full pointer-events-none">
                    <EndIcon
                        className="text-muted-foreground"
                        size={18}
                    />
                </div>
            )}
        </div>
    )
}

export { Input }
