'use client'

import { format, type Locale } from 'date-fns'

import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { Button } from './button'
import { Calendar } from './calendar'
import { CalendarIcon } from 'lucide-react'
import { cn } from '@/shared/lib/css'

interface DatePickerProps {
    locale: Locale
    date: Date | undefined
    setDate: (date: Date | undefined) => void
    isOpen: boolean
    setIsOpen: (isOpen: boolean) => void
    className?: string
    today?: Date
}

export function DatePicker({
    locale,
    date,
    setDate,
    isOpen,
    setIsOpen,
    className,
    today,
}: DatePickerProps) {
    return (
        <Popover
            open={isOpen}
            onOpenChange={setIsOpen}
        >
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    size={'default'}
                    data-empty={!date}
                    className={cn(
                        'data-[empty=true]:text-muted-foreground min-w-[280px]',
                        className,
                    )}
                >
                    {date ? format(date, 'PPP', { locale: locale }) : <span>Укажите дату</span>}
                    <CalendarIcon className="ml-auto" />
                </Button>
            </PopoverTrigger>
            <PopoverContent
                className="w-auto overflow-hidden p-0"
                align="start"
            >
                <Calendar
                    mode="single"
                    selected={date}
                    locale={locale}
                    disabled={{ dayOfWeek: [0, 6] }}
                    onSelect={(date) => {
                        setDate(date)
                        setIsOpen(false)
                    }}
                    today={today}
                />
            </PopoverContent>
        </Popover>
    )
}
